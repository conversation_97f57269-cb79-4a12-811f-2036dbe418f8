import <PERSON><PERSON><PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";
import ImageModule from "docxtemplater-image-module-free";
import { GoodMoralFormData, TemplateData } from "../types";

// For PDF conversion
import mammoth from "mammoth";
import jsPDF from "jspdf";

/**
 * Capitalizes the first letter of each word in a string
 */
const capitalizeWords = (text: string): string => {
  return text
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

/**
 * Generates the full name from form data
 */
export const generateFullName = (form: GoodMoralFormData): string => {
  return `${form["last-name"]} ${form["mid-name"]} ${form["mid-initial"]}`
    .trim()
    .replace(/\s+/g, " ");
};

/**
 * Prepares template data from form data
 */
const prepareTemplateData = (form: GoodMoralFormData): TemplateData => {
  const templateData: TemplateData = {
    prov: capitalizeWords(form.prov),
    municipal: capitalizeWords(form.municipal),
    "last-name": capitalizeWords(form["last-name"]),
    "mid-name": capitalizeWords(form["mid-name"]),
    "mid-initial": form["mid-initial"].toUpperCase(),
    age: form.age,
    "pos-address": capitalizeWords(form["pos-address"]),
    day: form.day,
    month: capitalizeWords(form.month),
    year: form.year,
    mayor: form.mayor.toUpperCase(),
    "ctc-no": form["ctc-no"],
    "or-no": form["or-no"],
  };

  if (form.image) {
    templateData.image = form.image;
  }

  return templateData;
};

/**
 * Validates required form fields
 */
export const validateForm = (form: GoodMoralFormData): boolean => {
  return !!(form["last-name"] && form.prov && form.municipal);
};

/**
 * Converts DOCX buffer to HTML using mammoth
 */
const convertDocxToHtml = async (docxBuffer: ArrayBuffer): Promise<string> => {
  const result = await mammoth.convertToHtml({ arrayBuffer: docxBuffer });
  return result.value;
};

/**
 * Converts HTML to PDF using jsPDF
 */
const convertHtmlToPdf = (html: string, filename: string): void => {
  // Create a temporary div to render the HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px';
  tempDiv.style.width = '210mm'; // A4 width
  tempDiv.style.fontFamily = 'Arial, sans-serif';
  tempDiv.style.fontSize = '12px';
  tempDiv.style.lineHeight = '1.4';
  tempDiv.style.color = '#000';
  tempDiv.style.backgroundColor = '#fff';
  document.body.appendChild(tempDiv);

  // Create PDF
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Convert HTML to PDF
  pdf.html(tempDiv, {
    callback: function (pdf) {
      // Clean up
      document.body.removeChild(tempDiv);

      // Download the PDF
      pdf.save(filename);
    },
    x: 10,
    y: 10,
    width: 190, // A4 width minus margins
    windowWidth: 794 // A4 width in pixels at 96 DPI
  });
};

/**
 * Generates and downloads the Good Moral Certificate document as PDF
 */
export const generateDocument = async (form: GoodMoralFormData): Promise<void> => {
  if (!validateForm(form)) {
    throw new Error("Please fill in all required fields");
  }

  const response = await fetch("/Good_Moral_Certificate.docx");
  if (!response.ok) {
    throw new Error("Failed to load template");
  }

  const content = await response.arrayBuffer();
  const zip = new PizZip(content);

  const modules = [];
  if (form.image) {
    const imageOpts = {
      centered: false,
      getImage: (tagValue: string) =>
        Uint8Array.from(atob(tagValue), (c) => c.charCodeAt(0)),
      getSize: (): [number, number] => [192, 192], // 2x2 inches at 72 DPI
    };
    modules.push(new ImageModule(imageOpts));
  }

  const doc = new Docxtemplater(zip, {
    modules,
    paragraphLoop: true,
    linebreaks: true,
  });

  const templateData = prepareTemplateData(form);
  doc.setData(templateData);
  doc.render();

  // Generate DOCX buffer
  const docxBuffer = doc.getZip().generate({ type: "arraybuffer" });

  // Convert DOCX to HTML
  const html = await convertDocxToHtml(docxBuffer);

  // Generate filename
  const fullName = generateFullName(form);
  const filename = `Good_Moral_Certificate(${fullName}).pdf`;

  // Convert HTML to PDF and download
  convertHtmlToPdf(html, filename);
};
