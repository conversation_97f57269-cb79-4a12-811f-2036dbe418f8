"use client";

import { useState } from "react";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// --- Types ---
type GoodMoralFormData = {
  "first-name": string;
  "mid-initial": string;
  "last-name": string;
  "mid-name": string;
  age: string;
  municipal: string;
  prov: string;
  "pos-address": string;
  day: string;
  month: string;
  year: string;
  mayor: string;
  "ctc-no": string;
  "or-no": string;
};

// --- Helpers ---
const capitalizeWords = (text: string): string =>
  text
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");

const generateFullName = (form: GoodMoralFormData): string =>
  `${form["first-name"]} ${form["mid-initial"]}. ${form["last-name"]}`.trim();

const validateForm = (form: GoodMoralFormData): boolean =>
  !!(form["last-name"] && form.prov && form.municipal);

// --- Main Generator ---
const generatePDF = async (form: GoodMoralFormData): Promise<void> => {
  if (!validateForm(form)) {
    alert("Please fill in all required fields");
    return;
  }

  const container = document.createElement("div");
  Object.assign(container.style, {
    width: "1123px",
    height: "794px",
    padding: "40px",
    fontFamily: "serif",
    background: "#fff",
    position: "fixed",
    top: "-9999px",
    left: "-9999px",
    color: "#000",
  });

  container.innerHTML = `
    <div style="text-align:center; font-size:36px; margin-bottom:20px;">
      Certificate of Good Moral Character
    </div>

    <table style="width:100%; border-collapse:collapse; font-size:18px; margin-bottom:20px;">
      <tr>
        <td style="width:30%; font-weight:bold;">Full Name:</td>
        <td style="border:1px solid #000; padding:5px;">
          ${generateFullName(form)}
        </td>
      </tr>
      <tr>
        <td style="font-weight:bold;">Age:</td>
        <td style="border:1px solid #000; padding:5px;">${form.age}</td>
      </tr>
      <tr>
        <td style="font-weight:bold;">Address:</td>
        <td style="border:1px solid #000; padding:5px;">
          ${capitalizeWords(form["pos-address"])}, ${capitalizeWords(form.municipal)}, ${capitalizeWords(form.prov)}
        </td>
      </tr>
    </table>

    <p style="font-size:16px; margin-top:20px;">
      Issued this ${form.day} day of ${capitalizeWords(form.month)}, ${form.year}.
    </p>

    <p style="font-size:16px; text-align:right; margin-top:60px;">
      <strong>${form.mayor.toUpperCase()}</strong><br />
      Municipal Mayor
    </p>
  `;

  document.body.appendChild(container);

  const canvas = await html2canvas(container, { scale: 2 });
  const imgData = canvas.toDataURL("image/png");

  const pdf = new jsPDF("landscape", "px", "a4");
  pdf.addImage(imgData, "PNG", 0, 0, 842, 595);
  pdf.save(`Good_Moral_Certificate(${generateFullName(form)}).pdf`);

  document.body.removeChild(container);
};

// --- Main Component ---
export default function CertificateGenerator() {
  const [form, setForm] = useState<GoodMoralFormData>({
    "first-name": "Brent",
    "mid-name": "Lemmuell",
    "mid-initial": "L",
    "last-name": "Ortega",
    age: "22",
    municipal: "Tanauan",
    prov: "Leyte",
    "pos-address": "Brgy. Mohon",
    day: "16",
    month: "July",
    year: "2025",
    mayor: "Juan Dela Cruz",
    "ctc-no": "123456789",
    "or-no": "987654321",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="p-6 max-w-xl mx-auto space-y-4">
      <h1 className="text-xl font-bold">Good Moral Certificate Form</h1>

      {Object.keys(form).map((key) => (
        <div key={key}>
          <label className="block mb-1 capitalize">{key.replace(/-/g, " ")}:</label>
          <input
            className="w-full p-2 border rounded"
            name={key}
            value={(form as any)[key]}
            onChange={handleChange}
          />
        </div>
      ))}

      <button
        className="bg-blue-600 text-white px-4 py-2 rounded"
        onClick={() => generatePDF(form)}
      >
        Download Certificate PDF
      </button>
    </div>
  );
}
