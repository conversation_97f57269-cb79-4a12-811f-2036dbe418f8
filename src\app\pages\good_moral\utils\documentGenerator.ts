import <PERSON>z<PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";
import ImageModule from "docxtemplater-image-module-free";
import { GoodMoralFormData, TemplateData } from "../types";

/**
 * Capitalizes the first letter of each word in a string
 */
const capitalizeWords = (text: string): string => {
  return text
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

/**
 * Generates the full name from form data
 */
export const generateFullName = (form: GoodMoralFormData): string => {
  return `${form["last-name"]} ${form["mid-name"]} ${form["mid-initial"]}`
    .trim()
    .replace(/\s+/g, " ");
};

/**
 * Prepares template data from form data
 */
const prepareTemplateData = (form: GoodMoralFormData): TemplateData => {
  const templateData: TemplateData = {
    prov: capitalizeWords(form.prov),
    municipal: capitalizeWords(form.municipal),
    "last-name": capitalizeWords(form["last-name"]),
    "mid-name": capitalizeWords(form["mid-name"]),
    "mid-initial": form["mid-initial"].toUpperCase(),
    age: form.age,
    "pos-address": capitalizeWords(form["pos-address"]),
    day: form.day,
    month: capitalizeWords(form.month),
    year: form.year,
    mayor: form.mayor.toUpperCase(),
    "ctc-no": form["ctc-no"],
    "or-no": form["or-no"],
  };

  if (form.image) {
    templateData.image = form.image;
  }

  return templateData;
};

/**
 * Validates required form fields
 */
export const validateForm = (form: GoodMoralFormData): boolean => {
  return !!(form["last-name"] && form.prov && form.municipal);
};

/**
 * Generates and downloads the Good Moral Certificate document
 */
export const generateDocument = async (form: GoodMoralFormData): Promise<void> => {
  if (!validateForm(form)) {
    throw new Error("Please fill in all required fields");
  }

  const response = await fetch("/Good_Moral_Certificate.docx");
  if (!response.ok) {
    throw new Error("Failed to load template");
  }

  const content = await response.arrayBuffer();
  const zip = new PizZip(content);

  const modules = [];
  if (form.image) {
    const imageOpts = {
      centered: false,
      getImage: (tagValue: string) =>
        Uint8Array.from(atob(tagValue), (c) => c.charCodeAt(0)),
      getSize: (): [number, number] => [192, 192], // 2x2 inches at 72 DPI
    };
    modules.push(new ImageModule(imageOpts));
  }

  const doc = new Docxtemplater(zip, {
    modules,
    paragraphLoop: true,
    linebreaks: true,
  });

  const templateData = prepareTemplateData(form);
  doc.setData(templateData);
  doc.render();

  const blob = doc.getZip().generate({ type: "blob" });
  const fullName = generateFullName(form);
  
  const file = new File(
    [blob],
    `Good_Moral_Certificate(${fullName}).docx`,
    {
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    }
  );

  const url = URL.createObjectURL(file);
  const a = document.createElement("a");
  a.href = url;
  a.download = file.name;
  a.click();
  a.remove();
  URL.revokeObjectURL(url);
};
